import React, { useEffect, useState } from "react";
import { useAtom } from "jotai";
import { staticContentsAtom } from "@/lib/atom";

export type AuthenticationPhase =
  | "initializing"
  | "connecting"
  | "processing"
  | "validating"
  | "redirecting"
  | "complete";

interface AuthProgressMessage {
  title: string;
  description: string;
  duration: number; // in milliseconds
}

interface DynamicAuthProgressProps {
  isIframe: boolean;
  onPhaseChange?: (phase: AuthenticationPhase) => void;
  onComplete?: () => void;
}

const DynamicAuthProgress: React.FC<DynamicAuthProgressProps> = ({
  isIframe,
  onPhaseChange,
  onComplete,
}) => {
  const [staticContents] = useAtom<any>(staticContentsAtom);
  const [currentPhase, setCurrentPhase] =
    useState<AuthenticationPhase>("initializing");
  const [progress, setProgress] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Define authentication phases with dynamic timing and user-friendly messages
  const authPhases: Record<AuthenticationPhase, AuthProgressMessage> = {
    initializing: {
      title:
        staticContents?.authentication?.initializing_title || "Getting Started",
      description: isIframe
        ? staticContents?.authentication?.initializing_iframe ||
          "Establishing secure connection with your application portal..."
        : staticContents?.authentication?.initializing_direct ||
          "Setting up your secure authentication session...",
      duration: 1000,
    },
    connecting: {
      title:
        staticContents?.authentication?.connecting_title ||
        "Connecting Securely",
      description: isIframe
        ? staticContents?.authentication?.connecting_iframe ||
          "Receiving your authentication credentials from the portal..."
        : staticContents?.authentication?.connecting_direct ||
          "Connecting to our secure authentication servers...",
      duration: 1500,
    },
    processing: {
      title:
        staticContents?.authentication?.processing_title ||
        "Verifying Your Identity",
      description: isIframe
        ? staticContents?.authentication?.processing_iframe ||
          "Validating your credentials and setting up your personalized session..."
        : staticContents?.authentication?.processing_direct ||
          "Securely verifying your identity and permissions...",
      duration: 2500,
    },
    validating: {
      title:
        staticContents?.authentication?.validating_title ||
        "Loading Your Information",
      description:
        staticContents?.authentication?.validating_desc ||
        "Retrieving your application details, preferences, and progress...",
      duration: 2000,
    },
    redirecting: {
      title:
        staticContents?.authentication?.redirecting_title ||
        "Preparing Your Experience",
      description:
        staticContents?.authentication?.redirecting_desc ||
        "Almost ready! Setting up your personalized dashboard...",
      duration: 1000,
    },
    complete: {
      title: staticContents?.authentication?.complete_title || "Welcome Back!",
      description:
        staticContents?.authentication?.complete_desc ||
        "Authentication successful. Taking you to your application now...",
      duration: 500,
    },
  };

  // Calculate total expected duration
  const totalDuration = Object.values(authPhases).reduce(
    (sum, phase) => sum + phase.duration,
    0
  );

  useEffect(() => {
    let phaseTimer: NodeJS.Timeout;
    let progressTimer: NodeJS.Timeout;
    let elapsedTimer: NodeJS.Timeout;

    const phases: AuthenticationPhase[] = [
      "initializing",
      "connecting",
      "processing",
      "validating",
      "redirecting",
      "complete",
    ];
    let currentPhaseIndex = 0;
    let phaseStartTime = Date.now();
    let totalStartTime = Date.now();

    const updateProgress = () => {
      const now = Date.now();
      const totalElapsed = now - totalStartTime;
      const phaseElapsed = now - phaseStartTime;
      const currentPhaseDuration =
        authPhases[phases[currentPhaseIndex]].duration;

      // Calculate overall progress
      let cumulativeDuration = 0;
      for (let i = 0; i < currentPhaseIndex; i++) {
        cumulativeDuration += authPhases[phases[i]].duration;
      }

      const phaseProgress = Math.min(phaseElapsed / currentPhaseDuration, 1);
      const overallProgress =
        ((cumulativeDuration + phaseProgress * currentPhaseDuration) /
          totalDuration) *
        100;

      setProgress(Math.min(overallProgress, 100));
      setElapsedTime(totalElapsed);
    };

    const advancePhase = () => {
      if (currentPhaseIndex < phases.length - 1) {
        currentPhaseIndex++;
        const newPhase = phases[currentPhaseIndex];
        setCurrentPhase(newPhase);
        onPhaseChange?.(newPhase);
        phaseStartTime = Date.now();

        if (newPhase === "complete") {
          setTimeout(() => {
            onComplete?.();
          }, authPhases[newPhase].duration);
        } else {
          phaseTimer = setTimeout(advancePhase, authPhases[newPhase].duration);
        }
      }
    };

    // Start the authentication flow
    phaseTimer = setTimeout(advancePhase, authPhases[phases[0]].duration);

    // Update progress every 100ms for smooth animation
    progressTimer = setInterval(updateProgress, 100);

    // Update elapsed time every second
    elapsedTimer = setInterval(() => {
      setElapsedTime(Date.now() - totalStartTime);
    }, 1000);

    return () => {
      clearTimeout(phaseTimer);
      clearInterval(progressTimer);
      clearInterval(elapsedTimer);
    };
  }, [isIframe, onPhaseChange, onComplete]);

  const currentPhaseData = authPhases[currentPhase];
  const formattedElapsedTime = Math.floor(elapsedTime / 1000);

  return (
    <div className="text-white text-center max-w-md mx-auto">
      {/* Main Title */}
      <h1 className="text-2xl mb-6 font-semibold">{currentPhaseData.title}</h1>

      {/* Animated Icon */}
      <div className="mb-6 flex justify-center">
        <div className="relative">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 100 100"
            width="80"
            height="80"
            className="text-white"
          >
            <defs>
              <linearGradient
                id="authGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="currentColor" stopOpacity="0.8" />
                <stop
                  offset="100%"
                  stopColor="currentColor"
                  stopOpacity="0.3"
                />
              </linearGradient>
            </defs>

            {/* Outer rotating ring */}
            <circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke="url(#authGradient)"
              strokeWidth="2"
              strokeDasharray="8,4"
              opacity="0.6"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                from="0 50 50"
                to="360 50 50"
                dur="3s"
                repeatCount="indefinite"
              />
            </circle>

            {/* Inner pulsing circle */}
            <circle cx="50" cy="50" r="25" fill="currentColor" opacity="0.2">
              <animate
                attributeName="r"
                values="20;30;20"
                dur="2s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="opacity"
                values="0.1;0.3;0.1"
                dur="2s"
                repeatCount="indefinite"
              />
            </circle>

            {/* Center icon */}
            <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.8" />
          </svg>
        </div>
      </div>

      {/* Description */}
      <p className="text-lg mb-6 text-white/90 leading-relaxed">
        {currentPhaseData.description}
      </p>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="w-full bg-white/20 h-2 rounded-full overflow-hidden">
          <div
            className="bg-white h-full transition-all duration-300 ease-out rounded-full"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Progress Info */}
      <div className="flex justify-between items-center text-sm text-white/70 mb-4">
        <span>{Math.round(progress)}% complete</span>
        <span>{formattedElapsedTime}s elapsed</span>
      </div>

      {/* Phase Indicator */}
      <div className="text-xs text-white/60 uppercase tracking-wider mb-4">
        {currentPhase.replace(/([A-Z])/g, " $1").trim()}
      </div>

      {/* Contextual Tips */}
      {currentPhase === "processing" && (
        <div className="text-xs text-white/50 italic">
          This may take a few moments while we securely process your
          information...
        </div>
      )}

      {currentPhase === "validating" && (
        <div className="text-xs text-white/50 italic">
          We&apos;re retrieving your latest application data to ensure
          everything is up to date...
        </div>
      )}

      {formattedElapsedTime > 5 && currentPhase !== "complete" && (
        <div className="text-xs text-white/50 italic mt-2">
          Thank you for your patience. We&apos;re ensuring everything is secure
          and ready for you...
        </div>
      )}
    </div>
  );
};

export default DynamicAuthProgress;
